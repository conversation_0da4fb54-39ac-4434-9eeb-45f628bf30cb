import { createTheme, Theme } from '@mui/material';
import '@mui/x-data-grid/themeAugmentation';

const defaultTypography = {
  fontFamily: "'Open Sans', sans-serif;",
};

const headerTypography = {
  fontFamily: "'Quicksand', sans-serif;",
  fontWeight: 700,
  letterSpacing: '-0.04em',
};

const buttonTypography = {
  fontFamily: "'Quicksand', sans-serif;",
  fontWeight: 700,
  textTransform: 'none' as const,
};

type ColorShade = 'light' | 'main' | 'dark' | 'contrastText';
export type MUIColor = 'inherit' | `${keyof Theme['palette']}.${ColorShade}`;

export type PaletteKey = Extract<keyof Theme['palette'], string>;

export type PaletteVariants<K extends PaletteKey> = Extract<keyof Theme['palette'][K], string>;

export type ThemeColor = PaletteKey | { [K in PaletteKey]: `${K}.${PaletteVariants<K>}` }[PaletteKey];

export const theme = createTheme({
  palette: {
    primary: {
      main: '#25c497',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#07ab9d',
      contrastText: '#ffffff',
    },
    success: {
      dark: 'rgb(7, 171, 157)',
      main: '#25c497',
    },
  },
  typography: {
    ...defaultTypography,
    h1: headerTypography,
    h2: headerTypography,
    h3: headerTypography,
    h4: headerTypography,
    h5: headerTypography,
    h6: headerTypography,
    button: buttonTypography,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 24,
        },
        sizeSmall: {
          fontSize: '0.875rem',
          lineHeight: '1.625',
        },
        sizeMedium: {
          fontSize: '0.9375rem',
          lineHeight: '1.633',
        },
        sizeLarge: {
          fontSize: '1rem',
          lineHeight: '1.641',
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          fontSize: '1rem',
          fontFamily: "'Open Sans', sans-serif;",
          color: 'rgba(0, 0, 0, 0.87)',
          minWidth: 'fit-content',
          borderBottom: '1px solid lightgray',
          fontWeight: 500,
          '&.Mui-selected': {
            color: 'rgba(0, 0, 0, 0.87)',
            fontWeight: 700,
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:last-child td': { border: 0 },
          '& th': { fontWeight: 'bold' },
        },
      },
    },
    MuiDataGrid: {
      styleOverrides: {
        root: {
          '& .MuiDataGrid-cell.link': {
            padding: 0,
            '&:focus, &:focus-within': {
              outline: 'none',
            },
          },
        },
      },
    },
  },
});
export default theme;

export function themeToColor(color: ThemeColor): string {
  if (color.includes('.')) {
    // If the value has a dot, split it into the palette key and variant key.
    const [paletteKey, variantKey] = color.split('.') as [PaletteKey, string];
    // At runtime you may want to check that variantKey exists, but at compile time this is safe.
    return theme.palette[paletteKey][variantKey as keyof (typeof theme.palette)[typeof paletteKey]] as string;
  }
  // Otherwise, assume it’s a valid palette key with a 'main' property.
  return theme.palette.primary.main;
}

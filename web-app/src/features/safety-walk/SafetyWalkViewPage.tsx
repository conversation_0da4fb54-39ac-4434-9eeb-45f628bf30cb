import {
  Autocomplete,
  Box,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Tab,
  Tabs,
  TextField,
  Typography,
  debounce,
  styled,
  Link,
} from '@mui/material';
import { Link as RouterLink, useParams } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers';
import SaveIcon from '@mui/icons-material/Save';
import PersonIcon from '@mui/icons-material/Person';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import PrintIcon from '@mui/icons-material/Print';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import dayjs, { Dayjs } from 'dayjs';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { LoadingButton, TabContext, TabPanel } from '@mui/lab';
import { useEffect, useState } from 'react';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import PageTitle from '../title/Title';
import {
  SafetyWalkFormInput,
  SafetyWalkObservationFormInput,
  SafetyWalkRead,
  SafetyWalkStatus,
  SafetyWalkStatusDisplayMap,
} from './safetyWalkTypes';
import SafetyWalkQuestionBlock from './SafetyWalkQuestionBlock';
import { useGetSafetyWalkQuery, useUpdateSafetyWalkMutation } from './safetyWalkApi';
import { useGetWorkPermitsQuery } from '../work-permit/workPermitApi';
import ErrorGate from '../../components/ErrorGate';
import useGuard, { GuardResult } from '../guard/guardHooks';
import { PermissionType } from '../guard/guardTypes';
import { UserRole } from '../user/userTypes';
import SafetyWalkScoreSummary from './SafetyWalkScoreSummary';
import { ObservationStatus } from '../observation/observationTypes';
import SafetyWalkPrintDialog from './SafetyWalkPrintDialog';
import Guard from '../guard/Guard';
import { useGetCurrentUserQuery } from '../user/userApi';
import CancelSafetyWalkDialog from './CancelSafetyWalkDialog';
import { ROUTES } from '../../constants';
import { isPastDayjs, isValidDayjs, preventSubmitOnEnter } from '../../utils';
import UnsavedChangesDialog from '../../components/UnsavedChangesDialog';
import CommentList from '../task/comment/CommentList';
import useTabs from '../../hooks/useTabs';
import SafetyWalkHistory from './SafetyWalkHistory';

const StyledStickyBox = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    position: 'sticky',
    top: '56px',
    zIndex: 10,
    paddingTop: '8px',
    backgroundColor: 'white',
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
    marginLeft: theme.spacing(-2),
    marginRight: theme.spacing(-2),
  },
})) as typeof Box;

const schema = yup.object({
  date: yup.mixed<Dayjs>().nullable().required().test(isValidDayjs).test(isPastDayjs).label('Date'),
  workPermit: yup.object().nullable().label('Work permit'),
  observations: yup
    .array()
    .of(
      yup.object().shape(
        {
          question: yup.object(),
          score: yup.string(),
          observation: yup
            .object()
            .nullable()
            .when('observation', {
              is: (o: SafetyWalkObservationFormInput) => o?.description !== null,
              then: yup.object().shape({
                description: yup.string().nullable().required().label('Description'),
                location: yup.object().nullable().required().label('Location'),
              }),
            }),
        },
        [['observation', 'observation']]
      )
    )
    .required(),
});

const defaultSchema = {
  date: dayjs().hour(0).minute(0).second(0).millisecond(0),
  template: null,
  workPermit: null,
  observations: [],
};

function SafetyWalkViewPage() {
  const { groupId, safetyWalkId } = useParams();
  const { tabValue, setTabValue } = useTabs('safetywalk');
  const guard = useGuard();
  const { data: safetyWalk, error: safetyWalkError } = useGetSafetyWalkQuery(Number(safetyWalkId), {
    skip: !safetyWalkId,
  });
  const canEdit = guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(PermissionType.OBSERVATION_UPDATE);
  const workPermitAndDateReadOnly = !canEdit || safetyWalk?.locked || !!safetyWalk?.observations.find((o) => o.locked);
  const {
    handleSubmit,
    control,
    formState: { isDirty },
    reset,
  } = useForm<SafetyWalkFormInput>({
    resolver: yupResolver(schema),
    defaultValues: defaultSchema,
  });
  const [updateSafetyWalk, result] = useUpdateSafetyWalkMutation();
  const [getPermits, setGetPermits] = useState<boolean>();
  const [search, setSearch] = useState<string>();
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState<boolean>(false);
  const { data: permits, isLoading: permitsLoading } = useGetWorkPermitsQuery(
    {
      ancestorGroupId: Number(groupId),
      search,
    },
    { skip: !getPermits }
  );
  const handleDelayedSearch = debounce((searchValue: string) => {
    setSearch(searchValue && searchValue.length > 0 ? searchValue : undefined);
  }, 200);

  const reportedGuard = useGuard(safetyWalk?.group.id);
  const { data: me } = useGetCurrentUserQuery();

  const canCancel = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) ||
    reportedGuard.hasPermission(PermissionType.OBSERVATION_CANCEL) ||
    safetyWalk?.createdBy?.id === me?.id;

  const [cancelSafetyWalk, setCancelSafetyWalk] = useState<SafetyWalkRead>();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCancel = () => {
    setAnchorEl(null);
    setCancelSafetyWalk(undefined);
  };

  const onSubmit = (form: SafetyWalkFormInput): void => {
    const formObservations = form.observations
      .filter((o) => o.answerRead?.status !== ObservationStatus.RESOLVED)
      .map((o) => ({
        id: Number(o.answerRead?.id),
        score: o.score,
        workPermit: form.workPermit !== null ? form.workPermit.id : null,
        question: o.question.id,
        category: o.category,
        cause: o.cause,
        observation:
          o.observation.description !== null && o.observation.location !== null && o.observation.files !== null
            ? {
                description: o.observation.description,
                location: o.observation.location.id,
                files: o.observation.files.map((f) => f.id),
              }
            : null,
      }));
    const newSafetyWalk = {
      id: Number(safetyWalkId),
      date: form.date.valueOf(),
      observations: formObservations,
    };
    updateSafetyWalk(newSafetyWalk);
  };

  useEffect(() => {
    if (safetyWalk) {
      const workPermit = safetyWalk.observations.find((o) => !!o.workPermit || o.workPermit !== null)?.workPermit;
      const safetyWalkObservations = [...safetyWalk.observations];
      const observations = safetyWalkObservations
        .sort((a, b) => a.question.id - b.question.id)
        .map((o) => ({
          answerRead: o,
          question: o.question,
          category: o.category,
          cause: o.cause,
          score: o.score,
          observation: o?.observation
            ? {
                description: o.observation.description,
                location: o.observation.location,
                files: o.observation.files,
              }
            : { description: null, location: null, files: null },
        }));
      const defaults = {
        workPermit: workPermit || null,
        date: dayjs(safetyWalk.date).hour(0).minute(0).second(0).millisecond(0),
        observations,
      };
      reset(defaults);
    }
  }, [safetyWalk, reset]);

  return (
    <ErrorGate error={safetyWalkError}>
      <form
        id="safety-walk-form"
        role="presentation"
        onSubmit={handleSubmit(onSubmit)}
        onKeyDown={preventSubmitOnEnter}
      >
        <PageTitle
          page={
            safetyWalk ? `#${safetyWalk?.sid} ${safetyWalk?.safetyWalkTemplate.name} - Safety walks` : 'Safety walks'
          }
        />
        <TabContext value={tabValue}>
          <StyledStickyBox>
            <Box display="flex" justifyContent="space-between" flexWrap="wrap">
              <Typography
                variant="h5"
                sx={{
                  mb: 1,
                  width: { xs: '100%', sm: 'unset' },
                }}
              >
                #{safetyWalk?.sid} - {safetyWalk?.safetyWalkTemplate.name}
              </Typography>
              <Box display="flex" gap={2} flexWrap="wrap" mb={{ xs: 1, md: 0 }}>
                {safetyWalk?.status !== SafetyWalkStatus.RESOLVED &&
                  safetyWalk?.status !== SafetyWalkStatus.CANCELED && (
                    <Box>
                      <LoadingButton
                        disabled={!isDirty}
                        loading={result.isLoading}
                        variant="contained"
                        type="submit"
                        form="safety-walk-form"
                        endIcon={<SaveIcon />}
                      >
                        {isDirty ? 'Save safety walk' : 'Safety walk saved'}
                      </LoadingButton>
                    </Box>
                  )}
                <IconButton sx={{ alignSelf: 'flex-end' }} color="inherit" onClick={() => setIsPrintDialogOpen(true)}>
                  <PrintIcon />
                </IconButton>
                {safetyWalk?.status !== SafetyWalkStatus.RESOLVED &&
                  safetyWalk?.status !== SafetyWalkStatus.CANCELED && (
                    <Guard hasAccess={canCancel}>
                      <Box>
                        <IconButton
                          color="inherit"
                          id="basic-button"
                          aria-controls={open ? 'basic-menu' : undefined}
                          aria-haspopup="true"
                          aria-expanded={open ? 'true' : undefined}
                          onClick={handleClick}
                        >
                          <MoreVertIcon />
                        </IconButton>

                        <Menu
                          id="basic-menu"
                          anchorEl={anchorEl}
                          open={open}
                          onClose={handleClose}
                          MenuListProps={{
                            'aria-labelledby': 'basic-button',
                          }}
                        >
                          <MenuItem onClick={() => setCancelSafetyWalk(safetyWalk)}>Cancel</MenuItem>
                        </Menu>
                      </Box>
                    </Guard>
                  )}
              </Box>
            </Box>
            <Box display="flex" gap={1} flexWrap="wrap">
              <Box display="inline" whiteSpace="nowrap">
                {safetyWalk && <SafetyWalkScoreSummary safetyWalk={safetyWalk} />}
              </Box>
              <Box display="inline" whiteSpace="nowrap">
                {safetyWalk && (
                  <>
                    {safetyWalk?.locked ? (
                      <LockIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />
                    ) : (
                      <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />
                    )}{' '}
                    {SafetyWalkStatusDisplayMap[safetyWalk.status]}
                  </>
                )}
              </Box>
              <Box display="inline" whiteSpace="nowrap">
                {safetyWalk && (
                  <>
                    <PeopleAltOutlinedIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                    {safetyWalk.group.name}
                  </>
                )}
              </Box>
              <Box display="inline" whiteSpace="nowrap">
                {safetyWalk && (
                  <>
                    <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                    {safetyWalk.createdBy.firstName} {safetyWalk.createdBy.lastName}
                  </>
                )}
              </Box>
            </Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Tabs value={tabValue} sx={{ borderBottom: '1px solid lightgray', width: '100%' }}>
                <Tab
                  sx={{ borderBottom: 0 }}
                  label="Safety walk"
                  value="safetywalk"
                  onClick={() => setTabValue('safetywalk')}
                />
                <Tab sx={{ borderBottom: 0 }} label="History" value="history" onClick={() => setTabValue('history')} />
              </Tabs>
            </Box>
          </StyledStickyBox>
          <TabPanel keepMounted value="safetywalk" sx={{ p: 0 }}>
            <Grid spacing={1} container>
              <Grid item xs={12} md={12} lg={9} xl={8}>
                <Paper sx={{ px: { xs: 2, md: 4 }, py: 2, borderRadius: 1 }} elevation={4}>
                  <Box display="flex" flex="1" gap={{ xs: 0, sm: 2 }} flexWrap={{ xs: 'wrap', sm: 'unset' }}>
                    <Controller
                      name="date"
                      control={control}
                      render={({ field, fieldState: { error } }) => (
                        <DatePicker
                          label="Date"
                          displayWeekNumber
                          disableFuture
                          format="DD/MM/YYYY"
                          value={field.value}
                          onChange={(newValue) =>
                            field.onChange(newValue ? newValue.hour(0).minute(0).second(0).millisecond(0) : newValue)
                          }
                          readOnly={workPermitAndDateReadOnly}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              autoComplete: 'off',
                              margin: 'normal',
                              size: 'small',
                              InputLabelProps: { shrink: true },
                              error: !!error,
                              helperText: error?.message,
                            },
                          }}
                        />
                      )}
                    />
                    <Controller
                      name="workPermit"
                      control={control}
                      render={({ field, fieldState: { error } }) => (
                        <Autocomplete
                          id="WorkPermit"
                          options={permits?.content || []}
                          loading={permitsLoading}
                          getOptionLabel={(option) => option.name}
                          renderOption={(props, option) => (
                            <li {...props} key={option.id}>
                              <Link
                                underline="hover"
                                mr={1}
                                component={RouterLink}
                                to={`./../../..${ROUTES.PTW}${ROUTES.WORK_PERMITS}/${option.id}`}
                              >
                                #{option.sid}
                              </Link>
                              {option.name}
                            </li>
                          )}
                          isOptionEqualToValue={(option, value) => option.id === value.id}
                          value={field.value}
                          filterOptions={(x) => x}
                          onInputChange={(event, newInputValue) => {
                            handleDelayedSearch(newInputValue);
                          }}
                          readOnly={workPermitAndDateReadOnly}
                          noOptionsText="No work permits"
                          fullWidth
                          onFocus={() => setGetPermits(true)}
                          onChange={(_, newValue) => field.onChange(newValue)}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work permit (optional)"
                              margin="normal"
                              fullWidth
                              placeholder={field.value !== null ? '' : 'None'}
                              error={!!error}
                              helperText={error?.message}
                              size="small"
                              slotProps={{
                                input: {
                                  ...params.InputProps,
                                  startAdornment:
                                    field.value == null ? undefined : (
                                      <Link
                                        underline="hover"
                                        component={RouterLink}
                                        to={`./../../..${ROUTES.PTW}${ROUTES.WORK_PERMITS}/${field.value.id}`}
                                      >
                                        #{field.value.sid}
                                      </Link>
                                    ),
                                },

                                inputLabel: {
                                  shrink: true,
                                },
                              }}
                            />
                          )}
                        />
                      )}
                    />
                  </Box>
                </Paper>
              </Grid>
              <Grid item xs={12} md={12} lg={9} xl={8}>
                <Controller
                  name="observations"
                  control={control}
                  render={({ field }) => (
                    <SafetyWalkQuestionBlock
                      observations={field.value}
                      control={control}
                      isEditing
                      locked={safetyWalk?.locked}
                      canEdit={canEdit}
                    />
                  )}
                />
                <CommentList processInstanceId={safetyWalk?.processInstanceId} />
              </Grid>
            </Grid>
          </TabPanel>
          <TabPanel value="history" sx={{ p: 0 }}>
            <SafetyWalkHistory safetyWalkId={Number(safetyWalkId)} processInstanceId={safetyWalk?.processInstanceId} />
          </TabPanel>
          <UnsavedChangesDialog isDirty={isDirty} />
          {cancelSafetyWalk && (
            <CancelSafetyWalkDialog open={!!cancelSafetyWalk} safetyWalk={cancelSafetyWalk} onClose={handleCancel} />
          )}
          {isPrintDialogOpen && (
            <SafetyWalkPrintDialog open={isPrintDialogOpen} onClose={() => setIsPrintDialogOpen(false)} />
          )}
        </TabContext>
      </form>
    </ErrorGate>
  );
}
export default SafetyWalkViewPage;

import SaveIcon from '@mui/icons-material/Save';
import PersonIcon from '@mui/icons-material/Person';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import PrintIcon from '@mui/icons-material/Print';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';

import { Link, useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Box, Grid, IconButton, Menu, MenuItem, Tab, Tabs, Typography, styled } from '@mui/material';
import { LoadingButton, TabContext, TabPanel } from '@mui/lab';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRight';
import ArrowLeftAltIcon from '@mui/icons-material/ArrowLeft';
import useGuard from '../guard/guardHooks';
import { useGetCurrentUserQuery } from '../user/userApi';
import {
  useGetShiftReportQuery,
  useGetShiftReportsNeighboursQuery,
  useUpdateShiftReportMutation,
} from './shiftReportApi';

import { useGetTasksQuery } from '../task/taskApi';
import { TaskDefinitionKey, TaskRead, TaskStatus } from '../task/taskTypes';
import { PermissionType } from '../guard/guardTypes';
import { UserRole } from '../user/userTypes';
import {
  ShiftReportFormInput,
  ShiftReportNeighbourParams,
  ShiftReportRead,
  ShiftReportStatus,
  ShiftReportStatusDisplayMap,
  ShiftReportSubTopic,
  ShiftReportSubTopicUpdate,
  ShiftReportUpdate,
} from './shiftReportTypes';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import TaskList from '../task/TaskList';
import ShiftReportForm from './ShiftReportForm';
import CancelShiftReportDialog from './CancelShiftReportDialog';
import { SubTopic, Topic } from './topic/topicTypes';
import ShiftReportHistory from './ShiftReportHistory';
import ShiftReportPrintDialog from './ShiftReportPrintDialog';
import useTaskSummary from '../task/taskHooks';
import TaskListTab from '../task/TaskListTab';
import CommentList from '../task/comment/CommentList';
import useTabs from '../../hooks/useTabs';

const StyledStickyBox = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    position: 'sticky',
    top: '56px',
    zIndex: 10,
    paddingTop: '8px',
    backgroundColor: 'white',
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
    marginLeft: theme.spacing(-2),
    marginRight: theme.spacing(-2),
  },
})) as typeof Box;

function ShiftReportViewPage() {
  const { shiftReportId, groupId } = useParams();
  const navigate = useNavigate();
  const { tabValue, setTabValue } = useTabs('shiftReport');
  const { data: me } = useGetCurrentUserQuery();
  const { data: shiftReport, error } = useGetShiftReportQuery(Number(shiftReportId), { skip: !shiftReportId });
  const shiftReportNeighbourParams: ShiftReportNeighbourParams = {
    id: Number(shiftReportId),
    ancestorGroupId: Number(groupId),
  };
  const { data: neighbours, error: neighboursError } = useGetShiftReportsNeighboursQuery(shiftReportNeighbourParams, {
    refetchOnMountOrArgChange: true,
  });
  const taskSummary = useTaskSummary(shiftReport?.processInstanceId);
  const { data: tasks } = useGetTasksQuery(
    {
      processInstanceId: shiftReport?.processInstanceId,
      status: TaskStatus.TODO,
      taskDefinitionKeyNot: TaskDefinitionKey.CUSTOM,
    },
    { skip: !shiftReport?.processInstanceId }
  );
  const guard = useGuard(shiftReport?.group.id, { skip: !shiftReport });
  const canEdit = guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(PermissionType.SHIFT_REPORT_UPDATE);
  const canCancel = guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(PermissionType.SHIFT_REPORT_CANCEL);

  const [isDirty, setIsDirty] = useState<boolean>(false);
  const [defaultValues, setDefaultValues] = useState<Partial<ShiftReportFormInput>>({});

  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState<boolean>(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const [cancelShiftReport, setCancelShiftReport] = useState<ShiftReportRead>();
  const handleCancel = () => {
    setAnchorEl(null);
    setCancelShiftReport(undefined);
  };

  useEffect(() => {
    const mapShiftReportSubtopicToSubTopic = (shiftReportSubtopic: ShiftReportSubTopic): SubTopic => {
      const logs = shiftReportSubtopic.logs?.map((log) => ({ ...log }));
      return { ...shiftReportSubtopic.subTopic, shiftReportSubTopicId: shiftReportSubtopic.id, logs };
    };

    const mapSubtTopicToTopic = (acc: Topic[], shiftReportSubtopic: ShiftReportSubTopic): Topic[] => {
      const subTopic = mapShiftReportSubtopicToSubTopic(shiftReportSubtopic);
      const existingTopic = acc.find((topic) => topic.id === shiftReportSubtopic.subTopic.topic.id);

      if (!existingTopic) {
        const topic: Topic = {
          ...shiftReportSubtopic.subTopic.topic,
          subTopics: [subTopic],
        };
        acc.push(topic);
      } else {
        existingTopic.subTopics.push(subTopic);
      }

      return acc;
    };

    // ? Map sub topic -> topic structure to topic -> sub topic structure, and create the desired update objects
    const topics: Topic[] = shiftReport?.subTopics?.reduce(mapSubtTopicToTopic, []) || [];

    // ? Sort the topics by order and name
    topics.sort((a, b) => {
      if (a.order !== b.order) {
        return a.order - b.order;
      }
      return a.name.localeCompare(b.name);
    });

    // ? Sort subTopics by order and name
    topics.forEach((topic) => {
      topic.subTopics?.sort((a, b) => {
        if (a.order !== b.order) {
          return a.order - b.order;
        }
        return a.name.localeCompare(b.name);
      });
    });

    // ? Sort logs by creation time
    topics.forEach((topic) => {
      topic.subTopics?.forEach((subTopic) => {
        subTopic.logs?.sort((a, b) => {
          if (a.id && b.id) {
            if (a.id !== b.id) {
              return a.id - b.id;
            }
          }
          return 0;
        });
      });
    });

    setDefaultValues({
      group: shiftReport?.group,
      startTime: shiftReport ? dayjs(shiftReport.startTime).second(0).millisecond(0) : null,
      endTime: shiftReport ? dayjs(shiftReport.endTime).second(0).millisecond(0) : null,
      topics,
      status: shiftReport?.status,
    });
  }, [shiftReport]);

  const [updateShiftReport, result] = useUpdateShiftReportMutation();

  const onSubmit = (form: ShiftReportFormInput): void => {
    setDefaultValues(form);
    if (form.startTime !== null && form.endTime !== null) {
      const { topics: formTopics } = form;

      const shiftReportSubTopicUpdate: ShiftReportSubTopicUpdate[] =
        formTopics.length !== 0
          ? formTopics.flatMap((topic) =>
              topic.subTopics.map((subTopic) => ({
                id: subTopic.shiftReportSubTopicId,
                subTopic: subTopic.id,
                logs:
                  subTopic.logs?.map((log) => ({
                    id: log.id,
                    sid: log.sid,
                    description: log.description,
                    referenceNumber: log?.referenceNumber,
                    priority: log?.priority,
                  })) || [],
              }))
            )
          : [];

      const shiftReportUpdate: ShiftReportUpdate = {
        id: Number(shiftReportId),
        startTime: form.startTime.valueOf(),
        endTime: form.endTime.valueOf(),
        subTopics: shiftReportSubTopicUpdate,
      };

      updateShiftReport(shiftReportUpdate);
    }
  };

  const onTaskComplete = (task: TaskRead) => {
    switch (task.taskDefinitionKey) {
      case 'accept_handover':
        navigate(`./../add`);
        break;
      default:
        break;
    }
  };

  return (
    <ErrorGate error={error}>
      <PageTitle
        page={shiftReport ? `#${shiftReport.sid} ${shiftReport.group.name} - Shift reports` : 'Shift reports'}
      />
      <TabContext value={tabValue}>
        <StyledStickyBox>
          <Box display="flex" justifyContent="space-between" flexWrap="wrap">
            <Typography variant="h5" sx={{ mb: 1, width: { xs: '100%', md: 'unset' } }}>
              #{shiftReport?.sid} - {shiftReport?.group.name}
            </Typography>
            <Box display="flex" gap={2} flexWrap="wrap" mb={{ xs: 1, md: 0 }}>
              {shiftReport?.status !== ShiftReportStatus.CLOSED &&
                shiftReport?.status !== ShiftReportStatus.CANCELED && (
                  <Box width={{ xs: '100%', sm: 'unset' }}>
                    <LoadingButton
                      fullWidth
                      disabled={!isDirty}
                      loading={result.isLoading}
                      variant="contained"
                      type="submit"
                      form="shift-report-form"
                      endIcon={<SaveIcon />}
                    >
                      {isDirty ? 'Save shift report' : 'Shift report saved'}
                    </LoadingButton>
                  </Box>
                )}
              <TaskList tasks={tasks?.content} me={me} isDirty={isDirty} onTaskComplete={onTaskComplete} />
              <IconButton sx={{ alignSelf: 'flex-end' }} color="inherit" onClick={() => setIsPrintDialogOpen(true)}>
                <PrintIcon />
              </IconButton>
              {canCancel &&
                shiftReport?.status !== ShiftReportStatus.CLOSED &&
                shiftReport?.status !== ShiftReportStatus.CANCELED && (
                  <Box>
                    <IconButton
                      color="inherit"
                      id="basic-button"
                      aria-controls={open ? 'basic-menu' : undefined}
                      aria-haspopup="true"
                      aria-expanded={open ? 'true' : undefined}
                      onClick={handleClick}
                    >
                      <MoreVertIcon />
                    </IconButton>

                    <Menu
                      id="basic-menu"
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleClose}
                      MenuListProps={{
                        'aria-labelledby': 'basic-button',
                      }}
                    >
                      <MenuItem onClick={() => setCancelShiftReport(shiftReport)}>Cancel</MenuItem>
                    </Menu>
                  </Box>
                )}
            </Box>
          </Box>
          <Box display="flex" gap={1} flexWrap="wrap">
            <Box display="inline" whiteSpace="nowrap">
              {shiftReport && (
                <>
                  <PeopleAltOutlinedIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                  {shiftReport.group.name}
                </>
              )}
            </Box>
            <Box display="inline" whiteSpace="nowrap">
              {shiftReport && (
                <>
                  <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                  {shiftReport.createdBy.firstName} {shiftReport.createdBy.lastName}
                </>
              )}
            </Box>
            <Box display="inline" whiteSpace="nowrap">
              {shiftReport && (
                <>
                  {shiftReport.locked ? (
                    <LockIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />
                  ) : (
                    <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />
                  )}{' '}
                  {ShiftReportStatusDisplayMap[shiftReport.status]}
                </>
              )}
            </Box>
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Tabs value={tabValue} sx={{ borderBottom: '1px solid lightgray', width: '100%' }}>
              <Tab
                sx={{ borderBottom: 0 }}
                label="Shift report"
                value="shiftReport"
                onClick={() => setTabValue('shiftReport')}
              />
              <Tab
                sx={{ borderBottom: 0 }}
                label={`Tasks ${taskSummary}`}
                value="task"
                onClick={() => setTabValue('task')}
              />
              <Tab sx={{ borderBottom: 0 }} label="History" value="history" onClick={() => setTabValue('history')} />
              {neighbours && (
                <Box display="inline-flex" width="100%" justifyContent="flex-end">
                  <IconButton
                    component={Link}
                    disabled={!neighbours?.hasPrevious || neighboursError !== undefined}
                    to={`./../../shift-reports/${neighbours.previous}`}
                    size="small"
                  >
                    <ArrowLeftAltIcon fontSize="large" />
                  </IconButton>
                  <IconButton
                    component={Link}
                    disabled={!neighbours?.hasNext || neighboursError !== undefined}
                    to={`./../../shift-reports/${neighbours.next}`}
                    size="small"
                  >
                    <ArrowRightAltIcon fontSize="large" />
                  </IconButton>
                </Box>
              )}
            </Tabs>
          </Box>
        </StyledStickyBox>
        <TabPanel keepMounted value="shiftReport" sx={{ p: 0 }}>
          <Grid spacing={2} container>
            <Grid item xs={12} md={12} lg={12} xl={9}>
              <ShiftReportForm
                currentUser={me}
                onSubmit={onSubmit}
                onDirtyChange={setIsDirty}
                submitError={result.isError}
                isEditing
                defaultValues={defaultValues}
                locked={shiftReport?.locked}
                canEdit={canEdit}
              />
              <CommentList processInstanceId={shiftReport?.processInstanceId} />
            </Grid>
          </Grid>
        </TabPanel>
        <TabPanel value="task" sx={{ p: 0 }}>
          <TaskListTab me={me} processInstanceId={shiftReport?.processInstanceId} readOnly={shiftReport?.locked} />
        </TabPanel>
        <TabPanel value="history" sx={{ p: 0 }}>
          <ShiftReportHistory
            processInstanceId={shiftReport?.processInstanceId}
            shiftReportId={Number(shiftReportId)}
          />
        </TabPanel>
        {isPrintDialogOpen && (
          <ShiftReportPrintDialog open={isPrintDialogOpen} onClose={() => setIsPrintDialogOpen(false)} />
        )}
        {cancelShiftReport && (
          <CancelShiftReportDialog open={!!cancelShiftReport} shiftReport={cancelShiftReport} onClose={handleCancel} />
        )}
      </TabContext>
    </ErrorGate>
  );
}

export default ShiftReportViewPage;

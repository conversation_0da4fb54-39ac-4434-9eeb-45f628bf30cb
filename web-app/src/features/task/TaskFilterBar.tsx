import {
  Autocomplete,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  debounce,
  IconButton,
  MenuItem,
  MenuList,
  Paper,
  Stack,
  styled,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { useMemo, useEffect, useState, useRef } from 'react';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import {
  TaskPriority,
  TaskPriorityDisplayMap,
  TaskPriorityIconColorMap,
  TaskStatusOptions,
  TaskViewState,
} from './taskTypes';
import { useGetGroupQuery, useGetGroupsQuery } from '../group/groupApi';
import { GroupListRead } from '../group/groupTypes';
import FormDialog from '../../components/FormDialog';
import { useAppDispatch, useAppSelector } from '../../store';
import { setTaskViewState } from './taskSlice';
import { useGetUsersQuery } from '../user/userApi';
import { UserType } from '../user/userTypes';
import { useGetLabelsQuery } from './label/labelApi';

const StyledTextField = styled(TextField)(() => ({
  '& .MuiInput-underline:before': { borderBottom: 'none' },
  '& .MuiInput-underline:after': { borderBottom: 'none' },
  '& .MuiInput-root:hover:before': { borderBottom: 'none !important' },
}));

const StyledAutoComplete = styled(Autocomplete)(({ theme }) => ({
  [theme.breakpoints.down('lg')]: {
    display: 'none',
  },
})) as typeof Autocomplete;

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  [theme.breakpoints.up('lg')]: {
    display: 'none',
  },
})) as typeof IconButton;

interface TaskFilterBarProps {
  groupId: number;
  resetPageNumber: () => void;
}

function TaskFilterBar({ groupId, resetPageNumber }: TaskFilterBarProps) {
  const taskViewState = useAppSelector((state) => state.task.taskViewState);
  const dispatch = useAppDispatch();
  const theme = useTheme();
  const smallScreen = useMediaQuery(theme.breakpoints.down('lg'));
  const defaultPageSize = 1000;

  const [isExpanded, setIsExpanded] = useState(false);
  const [loadGroups, setLoadGroups] = useState<boolean>(false);
  const [loadOwners, setLoadOwners] = useState<boolean>(false);
  const [loadAssignees, setLoadAssignees] = useState<boolean>(false);
  const [loadLabels, setLoadLabels] = useState<boolean>(false);

  const statusAutocompleteRef = useRef<HTMLDivElement>(null);
  const [statusMenuOpen, setStatusMenuOpen] = useState(false);

  const [searchValue, setSearchValue] = useState<string>(taskViewState.search || '');
  const { data: groups, isLoading: groupsLoading } = useGetGroupsQuery({ ancestorId: groupId }, { skip: !loadGroups });
  const { data: group, isLoading: groupLoading } = useGetGroupQuery(Number(groupId), { skip: !loadGroups });
  const { data: owners, isLoading: ownersLoading } = useGetUsersQuery(
    { taskOwnerAncestorGroupId: Number(groupId), pageSize: defaultPageSize, type: UserType.PERSON },
    { skip: !loadOwners }
  );
  const { data: assignees, isLoading: assigneesLoading } = useGetUsersQuery(
    {
      taskAssignedAncestorGroupId: Number(groupId),
      pageSize: defaultPageSize,
      type: UserType.PERSON,
    },
    { skip: !loadAssignees }
  );
  const { data: labels, isLoading: labelsLoading } = useGetLabelsQuery({ pageSize: 100 }, { skip: !loadLabels });

  const groupOptions = group && groups ? [group as GroupListRead].concat(groups) : [];
  const ownerOptions = owners?.content || [];
  const assigneeOptions = assignees?.content || [];

  const [filterDialogOpen, setFilterDialogOpen] = useState<boolean>(false);
  const [dialogParams, setDialogParams] = useState<TaskViewState>(taskViewState);
  const hasFilters =
    taskViewState.group ||
    taskViewState.status ||
    taskViewState.owner ||
    taskViewState.assignee ||
    (taskViewState.labels && taskViewState.labels.length > 0);

  useEffect(() => {
    setDialogParams(taskViewState);
  }, [taskViewState]);

  const handleDelayedSearch = useMemo(
    () =>
      debounce((search: string, state: TaskViewState) => {
        const newState = { ...state };
        newState.search = search && search.length > 0 ? search : undefined;
        dispatch(setTaskViewState(newState));
        resetPageNumber();
      }, 200),
    [dispatch, resetPageNumber]
  );

  const handleSearch = (search: string): void => {
    setSearchValue(search);
    handleDelayedSearch(search, taskViewState);
  };

  const clearSearch = (): void => {
    setSearchValue('');
    const newState = { ...taskViewState };
    newState.search = undefined;
    dispatch(setTaskViewState(newState));
    resetPageNumber();
  };

  const onDialogSubmit = (): void => {
    dispatch(setTaskViewState(dialogParams));
    resetPageNumber();
    setFilterDialogOpen(false);
  };

  const onDialogClose = (): void => {
    setFilterDialogOpen(false);
    setDialogParams(taskViewState);
  };

  const onDialogClear = (): void => {
    const newParams = { ...dialogParams };
    newParams.group = undefined;
    newParams.status = undefined;
    newParams.assignee = undefined;
    newParams.owner = undefined;
    newParams.labels = undefined;
    newParams.priority = undefined;
    setDialogParams(newParams);
  };

  return (
    <Paper elevation={4} sx={{ mt: 1 }}>
      <Stack py={1} px={2} direction="row">
        <StyledTextField
          sx={{
            flexGrow: 1,
            '.clearIndicator': { visibility: 'hidden' },
            ':hover .clearIndicator': { visibility: 'visible' },
            ':focus-within .clearIndicator': { visibility: 'visible' },
          }}
          InputProps={{
            startAdornment: <SearchIcon sx={{ color: 'rgba(0, 0, 0, 0.57)', mr: 1 }} fontSize="small" />,
            endAdornment: searchValue.length > 0 && (
              <IconButton size="small" onClick={clearSearch} className="clearIndicator">
                <ClearIcon fontSize="small" />
              </IconButton>
            ),
          }}
          placeholder="Search by task ID or name"
          margin="none"
          variant="standard"
          size="small"
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
        />
        <StyledAutoComplete
          id="group"
          options={groupOptions}
          getOptionLabel={(option) => option.name}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={taskViewState.group || null}
          onFocus={() => setLoadGroups(true)}
          loading={groupsLoading || groupLoading}
          onChange={(_, newValue) => {
            const newState = { ...taskViewState };
            const newGroup = newValue != null ? newValue : undefined;
            newState.group = newGroup;
            dispatch(setTaskViewState(newState));
            resetPageNumber();
          }}
          componentsProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Group"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="owner"
          options={ownerOptions}
          getOptionLabel={(option) => option.fullName}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={taskViewState.owner || null}
          onFocus={() => setLoadOwners(true)}
          loading={ownersLoading}
          onChange={(_, newValue) => {
            const newState = { ...taskViewState };
            const newUser = newValue != null ? newValue : undefined;
            newState.owner = newUser;
            dispatch(setTaskViewState(newState));
            resetPageNumber();
          }}
          componentsProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Owner"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="assignee"
          options={assigneeOptions}
          getOptionLabel={(option) => option.fullName}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={taskViewState.assignee || null}
          onFocus={() => setLoadAssignees(true)}
          loading={assigneesLoading}
          onChange={(_, newValue) => {
            const newState = { ...taskViewState };
            const newUser = newValue != null ? newValue : undefined;
            newState.assignee = newUser;
            dispatch(setTaskViewState(newState));
            resetPageNumber();
          }}
          componentsProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Assignee"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="status"
          options={TaskStatusOptions}
          getOptionLabel={(option) => {
            if (option.children && option.children.length > 0 && taskViewState.status) {
              const selectedChild = option.children.find((child) => child.value === taskViewState.status);
              if (selectedChild) {
                return selectedChild.label;
              }
            }
            return option.label;
          }}
          value={
            taskViewState?.status
              ? TaskStatusOptions.find(
                  (opt) =>
                    opt.value === taskViewState.status ||
                    (opt.children &&
                      opt.children.length > 0 &&
                      opt.children.some((child) => child.value === taskViewState.status))
                ) || null
              : null
          }
          onChange={(_, newValue) => {
            const newState = { ...taskViewState };
            newState.status = newValue?.value;
            dispatch(setTaskViewState(newState));
            resetPageNumber();
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Status"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '120px' }}
            />
          )}
          renderOption={(props, option) => (
            <Box
              component="li"
              {...props}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                const newState = { ...taskViewState };
                newState.status = option.value;
                dispatch(setTaskViewState(newState));
                resetPageNumber();
                const autocomplete = document.getElementById('status');
                if (autocomplete) {
                  (autocomplete as HTMLElement).blur();
                }
              }}
              sx={{
                position: 'relative',
                '&:hover > .MuiMenu-paper': {
                  display: 'block',
                },
                '&:hover': {
                  background: 'transparent',
                },
                pl: 0,
                '& > .MuiMenu-paper': {
                  transform: 'none !important',
                  position: 'fixed',
                },
              }}
            >
              {option.label}
              {option.children && (
                <>
                  <ChevronRightIcon
                    sx={{
                      ml: 3,
                      fontSize: 20,
                      color: 'rgba(0, 0, 0, 0.54)',
                      marginRight: '-8px',
                    }}
                  />
                  <MenuList
                    sx={{
                      display: option.children?.some((child) => child.value === taskViewState.status)
                        ? 'block'
                        : 'none',
                      position: 'absolute',
                      left: '100%',
                      top: 0,
                      marginLeft: 0.5,
                      zIndex: 1400,
                      minWidth: '150px',
                      backgroundColor: 'background.paper',
                      boxShadow: 1,
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: '-24px',
                        width: '24px',
                        height: 'calc(100% + 16px)',
                        background: 'transparent',
                      },
                    }}
                    className="MuiMenu-paper"
                  >
                    {option.children.map((child) => (
                      <MenuItem
                        key={child.value}
                        selected={child.value === taskViewState.status}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const newState = { ...taskViewState };
                          newState.status = child.value;
                          dispatch(setTaskViewState(newState));
                          resetPageNumber();
                          const autocomplete = document.getElementById('status');
                          if (autocomplete) {
                            (autocomplete as HTMLElement).blur();
                          }
                        }}
                        sx={{ pl: 1 }}
                      >
                        {child.label}
                      </MenuItem>
                    ))}
                  </MenuList>
                </>
              )}
            </Box>
          )}
        />
        {/* Priority dropdown */}
        <StyledAutoComplete
          id="priority"
          options={Object.values(TaskPriority)}
          getOptionLabel={(option) => TaskPriorityDisplayMap[option as TaskPriority]}
          value={taskViewState?.priority ? taskViewState.priority : null}
          onChange={(_, newValue) => {
            const newState = { ...taskViewState };
            newState.priority = newValue != null ? newValue : undefined;
            dispatch(setTaskViewState(newState));
            resetPageNumber();
          }}
          renderOption={(props, option) => (
            <li {...props}>
              <PriorityHighIcon sx={{ color: TaskPriorityIconColorMap[option as TaskPriority] }} />
              {TaskPriorityDisplayMap[option as TaskPriority]}
            </li>
          )}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Priority"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '120px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="labels"
          options={labels?.content || []}
          multiple
          disableCloseOnSelect
          getOptionLabel={(option) => option.name}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={taskViewState.labels || []}
          renderTags={(values) =>
            values.length > 0 && (
              <Box
                sx={{
                  display: 'inline',
                  width: '60px',
                  overflowX: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }}
              >
                {values[0].name}
                {values.length > 1 && `+${values.length - 1}`}
              </Box>
            )
          }
          onFocus={() => setLoadLabels(true)}
          loading={labelsLoading}
          onChange={(_, newValue) => {
            const newState = { ...taskViewState };
            const newLabels = newValue != null ? newValue : undefined;
            newState.labels = newLabels;
            dispatch(setTaskViewState(newState));
            resetPageNumber();
          }}
          componentsProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder={taskViewState?.labels && taskViewState?.labels.length > 0 ? '' : 'Labels'}
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledIconButton size="small" onClick={() => setFilterDialogOpen(true)}>
          <Badge variant="dot" color="primary" invisible={!hasFilters}>
            <FilterListIcon fontSize="small" />
          </Badge>
        </StyledIconButton>
      </Stack>
      <FormDialog
        open={smallScreen && filterDialogOpen}
        dialogTitleText="Filter tasks"
        submitButtonText="Apply filters"
        onClose={onDialogClose}
        onSubmit={onDialogSubmit}
      >
        <Autocomplete
          id="group"
          options={groupOptions}
          getOptionLabel={(option) => option.name}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={dialogParams.group || null}
          onFocus={() => setLoadGroups(true)}
          loading={groupsLoading || groupLoading}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            const newGroup = newValue != null ? newValue : undefined;
            newParams.group = newGroup;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField {...fieldParams} placeholder="Group" margin="normal" variant="outlined" size="small" fullWidth />
          )}
          slotProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
        />
        <Autocomplete
          id="owner"
          options={ownerOptions}
          getOptionLabel={(option) => option.fullName}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={dialogParams.owner || null}
          onFocus={() => setLoadOwners(true)}
          loading={ownersLoading}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            const newUser = newValue != null ? newValue : undefined;
            newParams.owner = newUser;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField {...fieldParams} placeholder="Owner" margin="normal" variant="outlined" size="small" fullWidth />
          )}
          slotProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
        />
        <Autocomplete
          id="assignee"
          options={assigneeOptions}
          getOptionLabel={(option) => option.fullName}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={dialogParams.assignee || null}
          onFocus={() => setLoadAssignees(true)}
          loading={assigneesLoading}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            const newUser = newValue != null ? newValue : undefined;
            newParams.assignee = newUser;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField
              {...fieldParams}
              placeholder="Assignee"
              margin="normal"
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
          slotProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
        />
        <Autocomplete
          id="status"
          options={TaskStatusOptions}
          ref={statusAutocompleteRef}
          open={statusMenuOpen}
          onOpen={() => setStatusMenuOpen(true)}
          onClose={() => setStatusMenuOpen(false)}
          getOptionLabel={(option) => {
            if (option.children && dialogParams.status) {
              const selectedChild = option.children.find((child) => child.value === dialogParams.status);
              if (selectedChild) {
                return selectedChild.label;
              }
            }
            return option.label;
          }}
          value={
            dialogParams?.status
              ? TaskStatusOptions.find(
                  (opt) =>
                    opt.value === dialogParams.status ||
                    (opt.children && opt.children.some((child) => child.value === dialogParams.status))
                ) || null
              : null
          }
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            newParams.status = newValue?.value;
            setDialogParams(newParams);
          }}
          renderOption={(props, option) => (
            <Box component="li" {...props} sx={{ flexDirection: 'column' }}>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'space-between',
                  pl: 2,
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                <Typography
                  onClick={() => {
                    if (!option.children) {
                      const newParams = { ...dialogParams };
                      newParams.status = option.value;
                      setDialogParams(newParams);
                      statusAutocompleteRef.current?.blur();
                    }
                  }}
                  sx={{
                    flex: 1,
                    cursor: 'pointer',
                    fontFamily: 'inherit',
                    fontSize: 'inherit',
                    fontWeight: 400,
                  }}
                >
                  {option.label}
                </Typography>
                {option.children && (
                  <Box
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsExpanded(!isExpanded);
                    }}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      ml: 2,
                      cursor: 'pointer',
                      width: '24px',
                      justifyContent: 'center',
                    }}
                  >
                    <KeyboardArrowDownIcon
                      sx={{
                        fontSize: 20,
                        color: 'rgba(0, 0, 0, 0.54)',
                        transform: isExpanded ? 'rotate(180deg)' : 'none',
                        transition: 'transform 0.2s',
                      }}
                    />
                  </Box>
                )}
              </Box>
              {option.children &&
                (isExpanded || option.children.some((child) => child.value === dialogParams.status)) && (
                  <Box sx={{ width: '100%' }}>
                    {option.children.map((child) => (
                      <MenuItem
                        key={child.value}
                        selected={child.value === dialogParams.status}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const newParams = { ...dialogParams };
                          newParams.status = child.value;
                          setDialogParams(newParams);
                          setIsExpanded(false);
                          setStatusMenuOpen(false);
                        }}
                        sx={{
                          pl: 4,
                          '&.Mui-selected': {
                            backgroundColor: 'grey',
                            color: 'white',
                          },
                        }}
                      >
                        {child.label}
                      </MenuItem>
                    ))}
                  </Box>
                )}
            </Box>
          )}
          renderInput={(fieldParams) => (
            <TextField
              {...fieldParams}
              placeholder="Status"
              margin="normal"
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
        />
        {/* Priority dropdown */}
        <Autocomplete
          id="priority"
          options={Object.values(TaskPriority)}
          getOptionLabel={(option) => TaskPriorityDisplayMap[option as TaskPriority]}
          value={dialogParams.priority ? dialogParams.priority : null}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            newParams.priority = newValue != null ? newValue : undefined;
            setDialogParams(newParams);
          }}
          renderOption={(props, option) => (
            <li {...props}>
              <PriorityHighIcon sx={{ color: TaskPriorityIconColorMap[option as TaskPriority] }} />
              {TaskPriorityDisplayMap[option as TaskPriority]}
            </li>
          )}
          renderInput={(params) => (
            <TextField {...params} placeholder="Priority" margin="normal" variant="outlined" size="small" fullWidth />
          )}
        />
        <Autocomplete
          id="methods"
          options={labels?.content || []}
          multiple
          disableCloseOnSelect
          getOptionLabel={(option) => option.name}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={dialogParams.labels || []}
          renderTags={(values) =>
            values.length > 0 && (
              <Box
                sx={{
                  ml: 1,
                  display: 'inline',
                  overflowX: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }}
              >
                {values[0].name}
                {values.length > 1 && `+${values.length - 1}`}
              </Box>
            )
          }
          onFocus={() => setLoadLabels(true)}
          loading={labelsLoading}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            const newLabels = newValue != null ? newValue : undefined;
            newParams.labels = newLabels;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField
              {...fieldParams}
              placeholder={dialogParams?.labels && dialogParams.labels.length > 0 ? '' : 'Labels'}
              margin="normal"
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
          slotProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
        />
        <Box mt={2}>
          <Button variant="outlined" fullWidth onClick={onDialogClear}>
            Clear all filters
          </Button>
        </Box>
      </FormDialog>
    </Paper>
  );
}
export default TaskFilterBar;

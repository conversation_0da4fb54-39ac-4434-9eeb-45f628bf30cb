import { Dayjs } from 'dayjs';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { PermissionType } from '../guard/guardTypes';
import { ProcessDisplay } from '../process/processTypes';
import { User, UserDisplay } from '../user/userTypes';
import { LabelRead } from './label/labelTypes';
import { FileDisplay } from '../file/fileTypes';
import { RecurrenceCreate, RecurrenceRead, RecurrenceUpdate } from '../recurrence/recurrenceTypes';
import { MUIIcon } from '../../utils';

export interface TaskParams {
  processInstanceId?: string;
  processInstanceIds?: string[];
  processDefinitionKeys?: string[];
  taskDefinitionKey?: string;
  taskDefinitionKeyNot?: TaskDefinitionKey;
  groupId?: number;
  ancestorGroupId?: number;
  candidateUsers?: number[];
  candidateGroups?: string[];
  assignee?: number;
  owner?: number;
  status?: TaskStatus;
  pageNumber?: number;
  pageSize?: number;
  search?: string;
  filter?: string;
  sort?: TaskSort[];
  labels?: number[];
  priority?: TaskPriority;
  startDate?: number;
  dueDate?: number;
  startDateGte?: number;
  startDateLte?: number;
  dueDateGte?: number;
  dueDateLte?: number;
}

export interface TaskPDFParams {
  id: string;
  timeZone: string;
}

export enum TaskPriority {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
}

export const TaskPriorityDisplayMap: Record<TaskPriority, string> = {
  HIGH: 'High',
  MEDIUM: 'Medium',
  LOW: 'Low',
};

export const TaskPriorityIconColorMap: Record<TaskPriority, string> = {
  [TaskPriority.HIGH]: 'error.main',
  [TaskPriority.MEDIUM]: 'warning.main',
  [TaskPriority.LOW]: 'action',
};

export interface TaskCreate {
  name: string;
  description?: string;
  group?: number;
  parentTaskId?: string | null;
  owner?: number;
  assignee?: number;
  dueDate?: number;
  processInstanceId?: string | null;
  labels: number[];
  files: number[];
  requiresVerification: boolean;
  priority: TaskPriority;
  startDate?: number;
  recurrence?: RecurrenceCreate;
}

export interface TaskUpdate {
  id: string;
  name?: string;
  description?: string;
  group?: number;
  owner?: number;
  assignee?: number;
  dueDate?: number;
  processInstanceId?: string | null;
  labels: number[];
  files: number[];
  requiresVerification: boolean;
  priority: TaskPriority;
  startDate?: number;
  recurrence?: RecurrenceUpdate;
}

export interface TaskFormInput {
  name: string;
  description?: string;
  processInstance?: ProcessDisplay | null;
  group?: GroupDisplay | null;
  parentTask?: TaskDisplay | null;
  owner?: UserDisplay | null;
  assignee?: UserDisplay | null;
  dueDate: Dayjs | null;
  labels: LabelRead[];
  files: FileDisplay[];
  requiresVerification: boolean;
  priority: TaskPriority;
  startDate: Dayjs | null;
  recurrence?: RecurrenceCreate;
}

export interface TaskDisplay {
  id: string;
  name: string;
}

export interface TaskRead {
  id: string;
  sid: string;
  name: string;
  group: GroupDisplay;
  status: TaskStatus;
  assignee?: UserDisplay;
  description: string;
  taskDefinitionKey: string;
  processInstance: ProcessDisplay;
  formKey: string | null;
  identityLinks: IdentityLinkRead[];
  owner: UserDisplay;
  dueDate?: number;
  parentTask: TaskDisplay | null;
  labels: LabelRead[];
  files: FileDisplay[];
  requiresVerification: boolean;
  priority: TaskPriority;
  startDate: number;
  recurrence?: RecurrenceRead;
  createTime: number;
  lastUpdatedTime: number;
}

export interface IdentityLinkRead {
  type: IdentityLinkType;
  userId?: string;
  groupId?: string;
}

export enum IdentityLinkType {
  CANDIDATE = 'candidate',
  EXCLUDE = 'exclude',
  CREATED_BY = 'createdBy',
  ASSIGNEE = 'assignee',
}

export enum TaskActionType {
  COMPLETE = 'COMPLETE',
  RESOLVE = 'RESOLVE',
}

export interface TaskAction {
  taskId: string;
  action: TaskActionType;
  formRepresentation?: FormRepresentation;
}

export enum FormFieldType {
  RADIO_BUTTONS = 'radio-buttons',
  GROUP_OF_PEOPLE = 'functional-group',
  PERSON = 'people',
  TEXT = 'text',
  INTEGER = 'integer',
  MULTI_LINE_TEXT = 'multi-line-text',
}

export interface FormFieldOption {
  id: string | null;
  name: string;
}

export interface FormRuleCondition {
  scope: string;
  failWhenUndefined: boolean;
  schema: {
    const: string;
  };
}

export interface FormRule {
  fieldId: string;
  effect: 'HIDE' | 'SHOW' | 'DISABLE' | 'ENABLE';
  condition: FormRuleCondition;
}

export interface FormField {
  id: string;
  name: string;
  type: FormFieldType;
  value: string | null;
  required: boolean;
  readOnly: boolean;
  placeholder: string | null;
  options: FormFieldOption[];
  params: {
    rule?: FormRule;
  };
}

export interface FormValue {
  id: string;
  value: string | null;
}

export interface FormModel {
  name: string;
  key: string;
  version: number;
  fields: FormField[];
}

export interface FormInfo {
  id: string;
  name: string;
  description: string;
  key: string;
  version: number;
  formModel: FormModel;
}

export interface FormRepresentationInput {
  formId: string;
  values: Record<string, unknown>;
  additionalValidations: boolean[];
}

export interface FormRepresentation {
  formId: string;
  values: Record<string, unknown>;
}

export interface TaskHistory {
  id: string;
  name: string;
  description: string;
  startTime: number;
  endTime: number;
  assignee: UserDisplay;
  formVariables: FormVariable[];
}

export interface FormVariable {
  id: string;
  task: string;
  name: string;
  type: string;
  text: string;
  text2: string;
  longValue: number;
  doubleValue: number;
  byteArrayId: string;
  time: number;
}

export interface TaskViewState {
  listView: 'mine' | 'all';
  taskDefinitionKey?: string;
  processDefinitionKeys?: string[];
  assignee?: User;
  owner?: User;
  search?: string;
  group?: GroupListRead;
  status?: TaskStatus | TaskTodoFilter;
  sort?: TaskSort[];
  labels?: LabelRead[];
  priority?: TaskPriority;
  columns?: TaskColumnSetting[];
  startDate?: number;
  dueDate?: number;
  startDateGte?: number;
  startDateLte?: number;
  dueDateGte?: number;
  dueDateLte?: number;
}

export interface TaskTabViewState {
  assignee?: User;
  owner?: User;
  search?: string;
  status?: TaskStatus;
  sort?: TaskSort[];
  labels?: LabelRead[];
  priority?: TaskPriority;
}

export interface TaskCopy {
  name: string;
  description?: string;
  processInstance?: ProcessDisplay | null;
  group?: GroupDisplay | null;
  parentTask?: TaskDisplay | null;
  owner?: UserDisplay | null;
  assignee?: UserDisplay | null;
  dueDate: Dayjs | null;
  labels: LabelRead[];
  files: FileDisplay[];
  requiresVerification: boolean;
  priority: TaskPriority;
  // TODO maybe remove this
  startDate: Dayjs | null;
  recurrence?: RecurrenceCreate;
}

export interface TaskState {
  taskCopy?: Partial<TaskCopy>;
  taskViewState: TaskViewState;
  taskTabViewState: TaskTabViewState;
}

export enum TaskStatus {
  TODO = 'TODO',
  TOVERIFY = 'TOVERIFY',
  COMPLETED = 'COMPLETED',
  CANCELED = 'CANCELED',
}

export enum TaskTodoFilter {
  LATE = 'LATE',
  DUETODAY = 'DUETODAY',
  DUETOMORROW = 'DUETOMORROW',
  DUETHISWEEK = 'DUETHISWEEK',
  DUENEXTWEEK = 'DUENEXTWEEK',
  ACTIVE = 'ACTIVE',
  PLANNED = 'PLANNED',
}

export const TaskStatusDisplayMap: Record<TaskStatus, string> = {
  TODO: 'To do',
  TOVERIFY: 'To verify',
  COMPLETED: 'Completed',
  CANCELED: 'Canceled',
};

export const TaskTodoFilterDisplayMap: Record<TaskTodoFilter, string> = {
  LATE: 'Late',
  DUETODAY: 'Due today',
  DUETOMORROW: 'Due tomorrow',
  DUETHISWEEK: 'Due this week',
  DUENEXTWEEK: 'Due next week',
  ACTIVE: 'Active',
  PLANNED: 'Planned',
};

export interface TaskStatusOption {
  value: TaskStatus | TaskTodoFilter;
  label: (typeof TaskTodoFilterDisplayMap)[TaskTodoFilter] | (typeof TaskStatusDisplayMap)[TaskStatus];
  children?: TaskStatusOption[];
}

export const TaskTodoFilterOptions: TaskStatusOption[] = [
  { value: TaskTodoFilter.LATE, label: TaskTodoFilterDisplayMap[TaskTodoFilter.LATE] },
  { value: TaskTodoFilter.DUETODAY, label: TaskTodoFilterDisplayMap[TaskTodoFilter.DUETODAY] },
  { value: TaskTodoFilter.DUETOMORROW, label: TaskTodoFilterDisplayMap[TaskTodoFilter.DUETOMORROW] },
  { value: TaskTodoFilter.DUETHISWEEK, label: TaskTodoFilterDisplayMap[TaskTodoFilter.DUETHISWEEK] },
  { value: TaskTodoFilter.DUENEXTWEEK, label: TaskTodoFilterDisplayMap[TaskTodoFilter.DUENEXTWEEK] },
  { value: TaskTodoFilter.ACTIVE, label: TaskTodoFilterDisplayMap[TaskTodoFilter.ACTIVE] },
  { value: TaskTodoFilter.PLANNED, label: TaskTodoFilterDisplayMap[TaskTodoFilter.PLANNED] },
];

export const TaskStatusOptions: TaskStatusOption[] = [
  {
    value: TaskStatus.TODO,
    label: TaskStatusDisplayMap[TaskStatus.TODO],
    children: TaskTodoFilterOptions,
  },
  { value: TaskStatus.TOVERIFY, label: TaskStatusDisplayMap[TaskStatus.TOVERIFY] },
  { value: TaskStatus.COMPLETED, label: TaskStatusDisplayMap[TaskStatus.COMPLETED] },
  { value: TaskStatus.CANCELED, label: TaskStatusDisplayMap[TaskStatus.CANCELED] },
];

export const TaskStatusIconMap: Record<TaskStatus, MUIIcon> = {
  [TaskStatus.COMPLETED]: LockIcon,
  [TaskStatus.CANCELED]: LockIcon,
  [TaskStatus.TOVERIFY]: LockIcon,
  [TaskStatus.TODO]: LockOpenIcon,
};

export enum TaskSortField {
  NAME = 'name',
  CREATE_TIME = 'create_time',
  LAST_UPDATED_TIME = 'last_updated_time',
  START_DATE = 'start_date',
  DUE_DATE = 'due_date',
  PRIORITY = 'priority',
}

export const TaskFieldSortMap: Partial<Record<keyof TaskRead, TaskSortField>> = {
  sid: TaskSortField.CREATE_TIME,
  name: TaskSortField.NAME,
  createTime: TaskSortField.CREATE_TIME,
  lastUpdatedTime: TaskSortField.LAST_UPDATED_TIME,
  startDate: TaskSortField.START_DATE,
  dueDate: TaskSortField.DUE_DATE,
  priority: TaskSortField.PRIORITY,
};

export interface TaskSort {
  field: TaskSortField;
  direction: 'asc' | 'desc';
}

export enum TaskDefinitionKey {
  CUSTOM = 'taskCustom',
}

export const ProcessDefinitionKeyModuleMap: Record<string, string> = {
  incidentProcess: 'Incidents',
  observation: 'Observations',
  safetyWalk: 'Safety walks',
  workPermitProcess: 'Work permits',
  instructionProcess: 'Instructions',
  devationProcess: 'Deviations',
  shiftReportProcess: 'Shift Reports',
  issueProcess: 'Issues',
  checklistProcess: 'Checklists',
  changeProcess: 'Changes',
  externalAuditProcess: 'External audits',
  internalAuditProcess: 'Internal audits',
  findingProcess: 'Findings',
  lototo: 'Lototo plan',
  lototoItem: 'Lototo item',
};

export const ProcessDefinitionKeyReadPermissionMap: Record<string, PermissionType> = {
  incidentProcess: PermissionType.INCIDENT_READ,
  observation: PermissionType.OBSERVATION_READ,
  safetyWalk: PermissionType.OBSERVATION_READ,
  workPermitProcess: PermissionType.WORKPERMIT_READ,
  instructionProcess: PermissionType.INSTRUCTION_READ,
  devationProcess: PermissionType.DEVIATION_READ,
  shiftReportProcess: PermissionType.SHIFT_REPORT_READ,
  issueProcess: PermissionType.CHANGE_READ,
  externalAuditProcess: PermissionType.FINDING_READ,
  internalAuditProcess: PermissionType.FINDING_READ,
  findingProcess: PermissionType.FINDING_READ,
  checklistProcess: PermissionType.CHANGE_READ,
  changeProcess: PermissionType.CHANGE_READ,
  lototo: PermissionType.LOTOTO_READ,
  lototoItem: PermissionType.LOTOTO_READ,
};

export const enum Module {
  HEALTH_AND_SAFETY = 'HEALTH_AND_SAFETY',
  PERMIT_TO_WORK = 'PERMIT_TO_WORK',
  COMMUNCIATION = 'COMMUNCIATION',
  CHANGE = 'CHANGE',
  COMPLIANCE = 'COMPLIANCE',
}

export const ModuleDisplayMap: Record<Module, string> = {
  HEALTH_AND_SAFETY: 'Health & Safety',
  PERMIT_TO_WORK: 'Permit to Work',
  COMMUNCIATION: 'Communication',
  CHANGE: 'Change',
  COMPLIANCE: 'Compliance',
};

export const ModuleProcess: Record<Module, string[]> = {
  HEALTH_AND_SAFETY: ['incidentProcess', 'observation', 'safetyWalk'],
  PERMIT_TO_WORK: ['workPermitProcess', 'lototo', 'lototoItem'],
  COMMUNCIATION: ['instructionProcess', 'deviationProcess', 'shiftReportProcess'],
  CHANGE: ['issueProcess', 'checklistProcess', 'changeProcess'],
  COMPLIANCE: ['findingProcess', 'internalAuditProcess', 'externalAuditProcess'],
};

export const enum TaskColumn {
  PRIORITY = 'priority',
  SID = 'sid',
  NAME = 'name',
  GROUP = 'group',
  ASSIGNEE = 'assignee',
  OWNER = 'owner',
  PROCESS_INSTANCE = 'processInstance',
  STATUS = 'status',
  START_DATE = 'startDate',
  DUE_DATE = 'dueDate',
  LABELS = 'labels',
  CREATED_AT = 'createTime',
  UPDATED_AT = 'lastUpdatedTime',
}

export const TaskColumnDisplayMap: Record<TaskColumn, string> = {
  [TaskColumn.PRIORITY]: 'Priority',
  [TaskColumn.SID]: 'ID',
  [TaskColumn.NAME]: 'Name',
  [TaskColumn.GROUP]: 'Group',
  [TaskColumn.ASSIGNEE]: 'Assignee',
  [TaskColumn.OWNER]: 'Owner',
  [TaskColumn.PROCESS_INSTANCE]: 'Linked item',
  [TaskColumn.STATUS]: 'Status',
  [TaskColumn.START_DATE]: 'Start date',
  [TaskColumn.DUE_DATE]: 'Due date',
  [TaskColumn.LABELS]: 'Labels',
  [TaskColumn.CREATED_AT]: 'Created at',
  [TaskColumn.UPDATED_AT]: 'Last updated at',
};

export interface TaskColumnSetting {
  column: TaskColumn;
  hidden: boolean;
  width: number;
}

export const TaskColumnDefaults: TaskColumnSetting[] = [
  {
    column: TaskColumn.PRIORITY,
    hidden: false,
    width: 30,
  },
  {
    column: TaskColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: TaskColumn.NAME,
    hidden: false,
    width: 300,
  },
  {
    column: TaskColumn.GROUP,
    hidden: false,
    width: 190,
  },
  {
    column: TaskColumn.OWNER,
    hidden: false,
    width: 190,
  },
  {
    column: TaskColumn.ASSIGNEE,
    hidden: false,
    width: 190,
  },
  {
    column: TaskColumn.STATUS,
    hidden: false,
    width: 130,
  },
  {
    column: TaskColumn.START_DATE,
    hidden: true,
    width: 200,
  },
  {
    column: TaskColumn.DUE_DATE,
    hidden: false,
    width: 200,
  },
  {
    column: TaskColumn.LABELS,
    hidden: false,
    width: 250,
  },
  {
    column: TaskColumn.PROCESS_INSTANCE,
    hidden: false,
    width: 250,
  },
  {
    column: TaskColumn.CREATED_AT,
    hidden: true,
    width: 200,
  },
  {
    column: TaskColumn.UPDATED_AT,
    hidden: true,
    width: 200,
  },
];

export enum TaskChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}
export const TaskChangeTypeDisplayMap: Record<TaskChangeType, string> = {
  INSERT: 'Task created',
  UPDATE: 'Task updated',
  DELETE: 'Task deleted',
};
export interface TaskChange {
  by: UserDisplay;
  at: number;
  type: TaskChangeType;
  oldEntity: TaskRead;
  newEntity: TaskRead;
}

export const TaskErrorMap: Record<string, Record<number, string>> = {
  bpmnTask_5: {
    400: 'All the answers need a description before submitting the checklist.',
  },
  bpmnTask_6: {
    400: 'All the questions need an answer before reporting the audit.',
  },
  LototoLockTask: {
    400: 'All LOTOTO items must complete the LOTOTO and verify LOTOTO tasks to ensure they are energy-free before locking the LOTOTO plan.',
  },
  LototoItemUNLOTOTOTask: {
    400: 'The LOTOTO plan should be unlocked before completing this task.',
  },
  LototoUnLockTask: {
    400: "The LOTOTO plan shouldn't have any issued work permits before completing this task.",
  },
};

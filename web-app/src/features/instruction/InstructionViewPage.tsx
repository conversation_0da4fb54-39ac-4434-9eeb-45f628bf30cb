import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { LoadingButton, TabContext, TabPanel } from '@mui/lab';
import { Box, Grid, Typography, styled, Tab, Tabs, Paper, IconButton, Menu, MenuItem } from '@mui/material';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import SaveIcon from '@mui/icons-material/Save';
import PrintIcon from '@mui/icons-material/Print';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import useGuard from '../guard/guardHooks';

import { useGetInstructionQuery, useUpdateInstructionMutation } from './instructionApi';
import { UserRole } from '../user/userTypes';
import { PermissionType } from '../guard/guardTypes';
import {
  InstructionFormInput,
  InstructionRead,
  InstructionStatus,
  InstructionStatusDisplayMap,
  InstructionUpdate,
} from './instructionTypes';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import InstructionForm from './InstructionForm';
import InstructionHistory from './InstructionHistory';
import InstructionPrintDialog from './InstructionPrintDialog';
import { useGetTasksQuery } from '../task/taskApi';
import { useGetCurrentUserQuery } from '../user/userApi';
import TaskList from '../task/TaskList';
import CancelInstructionDialog from './CancelInstructionDialog';
import { setInstructionCopy } from './instructionSlice';
import { useAppDispatch } from '../../store';
import { TaskDefinitionKey, TaskStatus } from '../task/taskTypes';
import useTaskSummary from '../task/taskHooks';
import TaskListTab from '../task/TaskListTab';
import CommentList from '../task/comment/CommentList';
import useTabs from '../../hooks/useTabs';

const StyledStickyBox = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    position: 'sticky',
    top: '56px',
    zIndex: 10,
    paddingTop: '8px',
    backgroundColor: 'white',
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
    marginLeft: theme.spacing(-2),
    marginRight: theme.spacing(-2),
  },
})) as typeof Box;

function InstructionViewPage() {
  const { instructionId } = useParams();
  const { tabValue, setTabValue } = useTabs('instruction');
  const { data: me } = useGetCurrentUserQuery();
  const { data: instruction, error } = useGetInstructionQuery(Number(instructionId), { skip: !instructionId });
  const taskSummary = useTaskSummary(instruction?.processInstanceId);
  const { data: tasks } = useGetTasksQuery(
    {
      processInstanceId: instruction?.processInstanceId,
      status: TaskStatus.TODO,
      taskDefinitionKeyNot: TaskDefinitionKey.CUSTOM,
    },
    { skip: !instruction?.processInstanceId }
  );
  const guard = useGuard(instruction?.group.id, { skip: !instruction });
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const canEdit = guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(PermissionType.INSTRUCTION_UPDATE);
  const canCancel = guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(PermissionType.INSTRUCTION_CANCEL);
  const [isDirty, setIsDirty] = useState<boolean>(false);
  const [defaultValues, setDefaultValues] = useState<Partial<InstructionFormInput>>();

  useEffect(() => {
    setDefaultValues({
      description: instruction?.description || '',
      startTime: instruction ? dayjs(instruction.startTime).second(0).millisecond(0) : undefined,
      endTime:
        instruction && dayjs(instruction.endTime).isValid()
          ? dayjs(instruction.endTime).second(0).millisecond(0)
          : null,
      group: instruction?.group || null,
      files: instruction?.files || [],
    });
  }, [instruction]);

  const [updateInstruction, result] = useUpdateInstructionMutation();
  const onSubmit = (form: InstructionFormInput): void => {
    setDefaultValues(form);
    if (form.startTime !== null && form.description !== null && form.group !== null) {
      const updatedInstruction: InstructionUpdate = {
        id: Number(instructionId),
        description: form.description,
        startTime: form.startTime.valueOf(),
        endTime: form.endTime?.valueOf(),
        files: form.files.map((f) => f.id),
      };
      updateInstruction(updatedInstruction);
    }
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCopy = () => {
    dispatch(
      setInstructionCopy({
        description: defaultValues?.description,
      })
    );
    setAnchorEl(null);
    navigate('./../add');
  };

  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState<boolean>(false);

  const [cancelInstruction, setCancelInstruction] = useState<InstructionRead>();
  const handleCancel = () => {
    setAnchorEl(null);
    setCancelInstruction(undefined);
    navigate('./../');
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page={instruction ? `#${instruction.sid} - Instructions` : 'Instructions'} />
      <TabContext value={tabValue}>
        <StyledStickyBox>
          <Box display="flex" justifyContent="space-between" flexWrap="wrap">
            <Typography variant="h5" sx={{ mb: 1, width: { xs: '100%', md: 'unset' } }}>
              #{instruction?.sid} - Instruction
            </Typography>
            <Box display="flex" gap={2} flexWrap="wrap" mb={{ xs: 1, md: 0 }}>
              {instruction?.status !== InstructionStatus.CLOSED &&
                instruction?.status !== InstructionStatus.CANCELED && (
                  <Box width={{ xs: '100%', sm: 'unset' }}>
                    <LoadingButton
                      fullWidth
                      disabled={!isDirty}
                      loading={result.isLoading}
                      variant="contained"
                      type="submit"
                      form="instruction-form"
                      endIcon={<SaveIcon />}
                    >
                      {isDirty ? 'Save instruction' : 'Instruction saved'}
                    </LoadingButton>
                  </Box>
                )}
              <TaskList tasks={tasks?.content} me={me} isDirty={isDirty} />
              <IconButton sx={{ alignSelf: 'flex-end' }} color="inherit" onClick={() => setIsPrintDialogOpen(true)}>
                <PrintIcon />
              </IconButton>
              <Box>
                <IconButton
                  color="inherit"
                  id="basic-button"
                  aria-controls={open ? 'basic-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                  onClick={handleClick}
                >
                  <MoreVertIcon />
                </IconButton>
                <Menu
                  id="basic-menu"
                  anchorEl={anchorEl}
                  open={open}
                  onClose={handleClose}
                  MenuListProps={{
                    'aria-labelledby': 'basic-button',
                  }}
                >
                  <MenuItem onClick={handleCopy}>Copy</MenuItem>
                  {canCancel &&
                    instruction?.status !== InstructionStatus.CLOSED &&
                    instruction?.status !== InstructionStatus.CANCELED && (
                      <MenuItem onClick={() => setCancelInstruction(instruction)}>Cancel</MenuItem>
                    )}
                </Menu>
              </Box>
            </Box>
          </Box>
          <Box display="flex" gap={1} flexWrap="wrap">
            <Box display="inline" whiteSpace="nowrap">
              {instruction && (
                <>
                  <PeopleAltOutlinedIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                  {instruction.group.name}
                </>
              )}
            </Box>
            <Box display="inline" whiteSpace="nowrap">
              {instruction && (
                <>
                  <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                  {instruction.createdBy.firstName} {instruction.createdBy.lastName}
                </>
              )}
            </Box>
            <Box display="inline" whiteSpace="nowrap">
              {instruction && (
                <>
                  {instruction.locked ? (
                    <LockIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />
                  ) : (
                    <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} />
                  )}{' '}
                  {InstructionStatusDisplayMap[instruction.status]}
                </>
              )}
            </Box>
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Tabs value={tabValue} sx={{ borderBottom: '1px solid lightgray', width: '100%' }}>
              <Tab
                sx={{ borderBottom: 0 }}
                label="Instruction"
                value="instruction"
                onClick={() => setTabValue('instruction')}
              />
              <Tab
                sx={{ borderBottom: 0 }}
                label={`Tasks ${taskSummary}`}
                value="task"
                onClick={() => setTabValue('task')}
              />
              <Tab sx={{ borderBottom: 0 }} label="History" value="history" onClick={() => setTabValue('history')} />
            </Tabs>
          </Box>
        </StyledStickyBox>
        <TabPanel keepMounted value="instruction" sx={{ p: 0 }}>
          <Grid spacing={4} container>
            <Grid item xs={12} md={9} xl={7}>
              <Paper sx={{ px: { xs: 2, md: 4 }, py: 2, borderRadius: 1 }} elevation={4}>
                <InstructionForm
                  onSubmit={onSubmit}
                  onDirtyChange={setIsDirty}
                  submitError={result.isError}
                  isEditing
                  defaultValues={defaultValues}
                  locked={instruction?.locked}
                  canEdit={canEdit}
                />
              </Paper>
              <CommentList processInstanceId={instruction?.processInstanceId} />
            </Grid>
          </Grid>
        </TabPanel>
        <TabPanel value="task" sx={{ p: 0 }}>
          <TaskListTab me={me} processInstanceId={instruction?.processInstanceId} readOnly={instruction?.locked} />
        </TabPanel>
        <TabPanel value="history" sx={{ p: 0 }}>
          <InstructionHistory
            instructionId={Number(instructionId)}
            processInstanceId={instruction?.processInstanceId}
          />
        </TabPanel>
        {isPrintDialogOpen && (
          <InstructionPrintDialog open={isPrintDialogOpen} onClose={() => setIsPrintDialogOpen(false)} />
        )}
        {cancelInstruction && (
          <CancelInstructionDialog open={!!cancelInstruction} instruction={cancelInstruction} onClose={handleCancel} />
        )}
      </TabContext>
    </ErrorGate>
  );
}

export default InstructionViewPage;
